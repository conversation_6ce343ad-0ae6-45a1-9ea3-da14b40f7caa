import os
import re
import shutil
import base64
import asyncio
import aiohttp
from PIL import Image
import logging
import json
import subprocess
from fastapi import FastAPI, Depends, HTTPException, Path
from fastapi.responses import JSONResponse
from pydantic import BaseModel
from typing import Optional  # 添加这一行以兼容 Python 3.9

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 全局文件锁（异步锁）
file_lock = asyncio.Lock()

# 新增异步函数：使用 curl 下载图片（保持原样，不做修改），添加日志输出
async def download_img(image_url, image_path_wait):
    loop = asyncio.get_event_loop()
    try:
        # 使用 run_in_executor 在线程池中运行同步 subprocess.run
        result = await loop.run_in_executor(None, lambda: subprocess.run(
            ['curl', '-k', '-L', '-o', image_path_wait, image_url], check=True, timeout=60, stdout=subprocess.PIPE,
            stderr=subprocess.PIPE))
        if result.returncode == 0 and os.path.exists(image_path_wait) and os.path.getsize(image_path_wait) > 0:
            logger.info(f"图片下载成功: {image_path_wait}, 文件大小: {os.path.getsize(image_path_wait)} 字节")
            return True, "下载成功"
        else:
            error_msg = f"curl 下载失败: 返回码 {result.returncode}, 输出: {result.stdout.decode()}, 错误: {result.stderr.decode()}"
            logger.error(error_msg)
            return False, error_msg
    except subprocess.CalledProcessError as e:
        error_msg = f"子进程错误: {str(e)}，输出: {e.output.decode()}, 错误: {e.stderr.decode()}"
        logger.error(error_msg)
        return False, error_msg
    except subprocess.TimeoutExpired as e:
        error_msg = f"下载超时: {str(e)}"
        logger.error(error_msg)
        return False, error_msg
    except Exception as e:
        error_msg = f"未知错误: {str(e)}"
        logger.error(error_msg)
        return False, error_msg

# 环境变量检查函数
def check_env_variables():
    required_vars = ["BASEURL", "APIKEY", "MODEL", "ZC_API_PASSWORD"]
    missing = [var for var in required_vars if not os.environ.get(var)]
    if missing:
        logger.error(f"缺少环境变量: {', '.join(missing)}")
        raise ValueError("配置缺失，请设置环境变量")

async def ai_universal_general(image_url=None, system_prompt="", user_prompt="", extract_rules=None):
    if not system_prompt and not user_prompt:
        logger.error("系统提示词和用户提示词均为空，无法进行AI调用")
        return "错误: 提示词不能为空"

    # 从环境变量读取 AI 配置（保持原样）
    ai_base_url = os.environ.get("BASEURL")
    ai_api_key = os.environ.get("APIKEY")
    ai_model = os.environ.get("MODEL")
    if not all([ai_base_url, ai_api_key, ai_model]):
        logger.error("AI配置缺失，请检查环境变量")
        return "错误: AI配置不完整"

    image_path_wait = None  # 用于存储图片路径，便于后续删除
    image_data_base64 = None

    # 处理图像（如果提供image_url）
    if image_url:
        if not image_url or not isinstance(image_url, str):
            logger.error("无效的image_url参数")
            return "错误: 无效的图像URL"

        local_dir_wait = "img"
        if not os.path.exists(local_dir_wait):
            os.makedirs(local_dir_wait)
        file_extension = ".png"
        local_filename_wait = f"img{file_extension}"
        image_path_wait = os.path.join(local_dir_wait, local_filename_wait)

        # 下载图片
        download_success, download_msg = await download_img(image_url, image_path_wait)
        if not download_success:
            logger.error(f"图片下载失败: {download_msg}")
            return f"错误: 下载失败 - {download_msg}"

        if not os.path.exists(image_path_wait) or os.path.getsize(image_path_wait) == 0:
            logger.error(f"错误: 图像文件不存在或下载失败: {image_path_wait}")
            return "错误: 下载失败 - 文件保存失败，请重试。"

        # 压缩图片（保持原样）
        async with file_lock:
            try:
                file_size = os.path.getsize(image_path_wait)
                if file_size > 300 * 1024:
                    with Image.open(image_path_wait) as img:
                        width, height = img.size
                        if width > 1200 or height > 1200:
                            scale = min(1200 / width, 1200 / height)
                            new_width = int(width * scale)
                            new_height = int(height * scale)
                            img = img.resize((new_width, new_height), Image.LANCZOS)
                        quality = 85
                        max_retries_compress = 5
                        attempt_compress = 0
                        temp_path = image_path_wait + ".temp"
                        while file_size > 300 * 1024 and attempt_compress < max_retries_compress and quality > 20:
                            img.save(temp_path, "PNG", optimize=True, quality=quality)
                            temp_size = os.path.getsize(temp_path)
                            if temp_size <= 300 * 1024:
                                os.replace(temp_path, image_path_wait)
                                file_size = temp_size
                                logger.info(f"图片压缩成功。压缩后大小: {file_size} 字节，质量设置为 {quality}")
                                break
                            quality -= 10
                            attempt_compress += 1
                        if os.path.exists(temp_path):
                            os.remove(temp_path)
                        if file_size > 300 * 1024:
                            if img.mode == 'RGBA':
                                img = img.convert('RGB')
                            quality = 70
                            attempt_compress = 0
                            while file_size > 300 * 1024 and attempt_compress < max_retries_compress and quality > 20:
                                img.save(temp_path, "JPEG", optimize=True, quality=quality)
                                temp_size = os.path.getsize(temp_path)
                                if temp_size <= 300 * 1024:
                                    os.replace(temp_path, image_path_wait)
                                    file_size = temp_size
                                    logger.info(
                                        f"图片转换为JPEG并压缩成功。压缩后大小: {file_size} 字节，质量设置为 {quality}")
                                    break
                                quality -= 10
                                attempt_compress += 1
                            if os.path.exists(temp_path):
                                os.remove(temp_path)
                        if file_size > 300 * 1024:
                            logger.warning(f"警告: 图片大小仍超过300KB ({file_size} 字节)，可能影响AI处理效率。")

            except Exception as e:
                logger.error(f"图片压缩失败: {str(e)}")

        # 读取并base64编码图像
        try:
            with open(image_path_wait, "rb") as image_file:
                image_data = image_file.read()
                image_data_base64 = base64.b64encode(image_data).decode('utf-8')
        except Exception as e:
            logger.error(f"读取图像文件失败: {str(e)}")
            # 如果读取失败，删除已下载的文件（如果存在）
            if image_path_wait and os.path.exists(image_path_wait):
                try:
                    os.remove(image_path_wait)
                    logger.info(f"删除图像文件成功: {image_path_wait}")
                except Exception as del_e:
                    logger.error(f"删除图像文件失败: {str(del_e)}")
            return "错误: 读取图像失败，请检查文件。"

    # AI请求部分，添加重试机制
    max_retries = 5
    backoff_factor = 2
    for attempt in range(max_retries):
        try:
            async with aiohttp.ClientSession() as session:
                # 构建user content，支持文本和图像
                user_content = []
                if user_prompt:
                    user_content.append({"type": "text", "text": user_prompt})
                if image_data_base64:
                    user_content.append(
                        {"type": "image_url", "image_url": {"url": f"data:image/png;base64,{image_data_base64}"}})

                payload = {
                    "messages": [
                        {"role": "system", "content": system_prompt},
                        {"role": "user", "content": user_content}
                    ],
                    "stream": False,
                    "model": ai_model,
                    "temperature": 0.5,  # 可以根据需要调整
                    "top_p": 1,  # 可以根据需要调整
                }
                logger.debug(f"AI 请求 payload: {payload}")

                async with session.post(
                        f"{ai_base_url}/v1/chat/completions",
                        headers={"Authorization": f"Bearer {ai_api_key}"},
                        json=payload,
                        timeout=60
                ) as response:
                    response.raise_for_status()
                    result_json = await response.json()
                    logger.debug(f"AI 响应: {result_json}")
                    content = result_json['choices'][0]['message']['content']
                    logger.info("AI响应成功")

                    # 处理提取规则
                    if extract_rules and isinstance(extract_rules, dict):
                        extracted_result = {}
                        for key, pattern in extract_rules.items():
                            match = re.search(pattern, content, re.DOTALL)
                            extracted_value = match.group(1).strip() if match else "提取失败"
                            extracted_result[key] = extracted_value
                        # 返回提取结果前，删除图片文件（如果存在）
                        if image_path_wait and os.path.exists(image_path_wait):
                            try:
                                os.remove(image_path_wait)
                                logger.info(f"删除图像文件成功: {image_path_wait}")
                            except Exception as del_e:
                                logger.error(f"删除图像文件失败: {str(del_e)}")
                        return extracted_result  # 返回提取结果字典
                    else:
                        # 如果没有提取规则，返回原始内容
                        # 返回前，删除图片文件（如果存在）
                        if image_path_wait and os.path.exists(image_path_wait):
                            try:
                                os.remove(image_path_wait)
                                logger.info(f"删除图像文件成功: {image_path_wait}")
                            except Exception as del_e:
                                logger.error(f"删除图像文件失败: {str(del_e)}")
                        return content

        except aiohttp.ClientError as e:
            if attempt == max_retries - 1:
                logger.error(f"AI 判断失败 (尝试 {max_retries} 次): {str(e)}")
                # AI调用失败后，删除图片文件（如果存在）
                if image_path_wait and os.path.exists(image_path_wait):
                    try:
                        os.remove(image_path_wait)
                        logger.info(f"删除图像文件成功: {image_path_wait}")
                    except Exception as del_e:
                        logger.error(f"删除图像文件失败: {str(del_e)}")
                return f"错误: AI服务请求失败 - {str(e)}"
            logger.warning(f"AI请求错误: {str(e)}，尝试 {attempt + 1}/{max_retries}")
            await asyncio.sleep(backoff_factor ** attempt)

    # 如果所有重试失败，删除图片文件（如果存在）
    if image_path_wait and os.path.exists(image_path_wait):
        try:
            os.remove(image_path_wait)
            logger.info(f"删除图像文件成功: {image_path_wait}")
        except Exception as del_e:
            logger.error(f"删除图像文件失败: {str(del_e)}")
    return "错误: AI服务多次请求失败，请稍后再试"


# AI 判断函数
async def ai_judgment(image_url, from_who, player_list_str=None):
    if not image_url or not isinstance(image_url, str):
        logger.error("无效的image_url参数")
        return "判断失败: 无效的图像URL"
    if not isinstance(from_who, (int, str)):
        logger.error("无效的from_who参数")
        return "判断失败: 无效的用户ID"
    # 从环境变量读取 AI 配置
    ai_base_url = os.environ.get("BASEURL")
    ai_api_key = os.environ.get("APIKEY")
    ai_model = os.environ.get("MODEL")
    # 检查 AI 配置是否完整（已在启动时检查，但这里也保留冗余检查）
    if not all([ai_base_url, ai_api_key, ai_model]):
        logger.error("AI配置缺失，请检查环境变量")
        return "判断失败: AI配置不完整"
    # 如果提供了玩家名单，添加到用户内容中
    if player_list_str:
        player_list_prompt = f"当前服务器玩家名单: {player_list_str}\n"
    else:
        player_list_prompt = ""
    local_dir_wait = "zc_wait_to_judgment"
    if not os.path.exists(local_dir_wait):
        os.makedirs(local_dir_wait)
    file_extension = ".png"
    local_filename_wait = f"zc_{from_who}{file_extension}"
    image_path_wait = os.path.join(local_dir_wait, local_filename_wait)
    # 下载图片，使用异步线程池调用 curl（使用提供的 download_with_curl 函数）
    download_success, download_msg = await download_img(image_url, image_path_wait)
    if not download_success:
        logger.error(f"图片下载失败: {download_msg}")
        return f"下载失败: {download_msg}"
    if not os.path.exists(image_path_wait) or os.path.getsize(image_path_wait) == 0:
        logger.error(f"错误: 图像文件不存在或下载失败: {image_path_wait}")
        return "下载失败: 文件保存失败，请重试。"
    # 使用锁保护文件操作（保持原样）
    async with file_lock:
        try:
            # 处理图片大小，降低到300KB以下（保持原样）
            file_size = os.path.getsize(image_path_wait)
            if file_size > 300 * 1024:
                with Image.open(image_path_wait) as img:
                    width, height = img.size
                    if width > 1200 or height > 1200:
                        scale = min(1200 / width, 1200 / height)
                        new_width = int(width * scale)
                        new_height = int(height * scale)
                        img = img.resize((new_width, new_height), Image.LANCZOS)
                    quality = 85
                    max_retries_compress = 5
                    attempt_compress = 0
                    temp_path = image_path_wait + ".temp"
                    while file_size > 300 * 1024 and attempt_compress < max_retries_compress and quality > 20:
                        img.save(temp_path, "PNG", optimize=True, quality=quality)
                        temp_size = os.path.getsize(temp_path)
                        if temp_size <= 300 * 1024:
                            os.replace(temp_path, image_path_wait)
                            file_size = temp_size
                            logger.info(f"图片压缩成功。压缩后大小: {file_size} 字节，质量设置为 {quality}")
                            break
                        quality -= 10
                        attempt_compress += 1
                    if os.path.exists(temp_path):
                        os.remove(temp_path)
                    if file_size > 300 * 1024:
                        if img.mode == 'RGBA':
                            img = img.convert('RGB')
                        quality = 70
                        attempt_compress = 0
                        while file_size > 300 * 1024 and attempt_compress < max_retries_compress and quality > 20:
                            img.save(temp_path, "JPEG", optimize=True, quality=quality)
                            temp_size = os.path.getsize(temp_path)
                            if temp_size <= 300 * 1024:
                                os.replace(temp_path, image_path_wait)
                                file_size = temp_size
                                logger.info(f"图片转换为JPEG并压缩成功。压缩后大小: {file_size} 字节，质量设置为 {quality}")
                                break
                            quality -= 10
                            attempt_compress += 1
                        if os.path.exists(temp_path):
                            os.remove(temp_path)
                    if file_size > 300 * 1024:
                        logger.warning(f"警告: 图片大小仍超过300KB ({file_size} 字节)，可能影响AI处理效率。")
        except Exception as e:
            logger.error(f"图片压缩失败: {str(e)}")
    # AI请求部分，修改用户内容，添加玩家名单
    max_retries = 5
    backoff_factor = 2
    for attempt in range(max_retries):
        try:
            async with aiohttp.ClientSession() as session:
                with open(image_path_wait, "rb") as image_file:
                    image_data = image_file.read()
                encoded_image = base64.b64encode(image_data).decode('utf-8')
                system_prompt = "You are a Server Administrator of the Battlefield Five community, and you need to deal with reports from group members fairly."
                # 修改用户内容，添加玩家名单提示
                user_content_text = f"""
                这是我给你提供的当前服务器的玩家名单:{player_list_prompt if player_list_prompt else ""}\n
                参照玩家名单来帮助你核对你通过图片提取的用户名，如果我给你的玩家名单为空，或者和你自己提取的玩家用户名几乎完全没有相近的，那就直接使用你自己提取出来的用户名。
                请分析图片中的战地五聊天对话框内容，用户可能会上传全屏图片，聊天对话框通常在左上角。在对话框中，红色字体表示敌方玩家用户名，蓝色字体表示己方玩家用户名。
                任务步骤：
                1. 提取并列出图片中的所有对话内容，保持原始格式
                2. 分析对话中是否存在语言攻击行为
                   - 语言攻击包括但不限于使用"fw"，"ez"，"si ma","wo cao ni ma"等侮辱性词汇
                   - 注意玩家可能使用拼音或各种变形来规避检测
                   - 使用提供的玩家名单参考，判断用户名是否为服务器中的有效玩家
                3. 注意不要过于敏感，只有"fw"，"ez"，"si ma","wo cao ni ma","cnm"被认定为语言攻击行为
                分析结果请按以下格式输出：
                <chat>
                [此处列出提取的所有对话]
                </chat>
                <judgment>
                [在此处仅填入数字：0表示没有检测到语言攻击，1表示检测到语言攻击]
                </judgment>
                <player>
                [如果检测到语言攻击，在此处列出进行语言攻击的玩家用户名，多个玩家用英文逗号分隔，如果玩家的id在提供的玩家名单里则在他的id前加上“确认在游戏中”，不在就不加]
                </player>
                <all_player>
                [输出对话中所有的玩家用户名，多个玩家用英文逗号分隔，如果玩家的id在提供的玩家名单里则在他的id前加上“确认在游戏中”，不在就不加]
                </all_player>
                <分析说明>
                [简要说明判断理由，指出哪些言论构成了语言攻击及其原因]
                </分析说明>
                """
                payload = {
                    "messages": [
                        {"role": "system", "content": system_prompt},
                        {"role": "user", "content": [
                            {"type": "text", "text": user_content_text},
                            {"type": "image_url", "image_url": {"url": f"data:image/png;base64,{encoded_image}"}}
                        ]}
                    ],
                    "stream": False,
                    "model": ai_model,
                    "temperature": 0.5,
                    "top_p": 1,
                }
                logger.debug(f"AI 请求 payload: {payload}")
                async with session.post(
                        f"{ai_base_url}/v1/chat/completions",
                        headers={"Authorization": f"Bearer {ai_api_key}"},
                        json=payload,
                        timeout=60
                ) as response:
                    response.raise_for_status()
                    result_json = await response.json()
                    logger.debug(f"AI 响应: {result_json}")
                    content = result_json['choices'][0]['message']['content']
                    logger.info("AI响应成功")
                    logger.debug(content)
                    judgment_match = re.search(r'<judgment>(.*?)</judgment>', content, re.DOTALL)
                    player_match = re.search(r'<player>(.*?)</player>', content, re.DOTALL)
                    chat_match = re.search(r'<chat>(.*?)</chat>', content, re.DOTALL)
                    all_palyer_match = re.search(r'<all_player>(.*?)</all_player>', content, re.DOTALL)
                    judgment_value = judgment_match.group(1).strip() if judgment_match else "提取失败"
                    player_value = player_match.group(1).strip() if player_match else "提取失败"
                    chat_value = chat_match.group(1).strip() if chat_match else "提取失败"
                    all_player_value = all_palyer_match.group(1).strip() if player_match else "提取失败"
                    if judgment_value == "1" and player_value != "提取失败":
                        cleaned_player_value = re.sub(r'[\W_]+', '_', player_value)
                        target_dir = "zc"
                        if not os.path.exists(target_dir):
                            os.makedirs(target_dir)
                        new_filename = f"zc_{from_who}_{cleaned_player_value}.png"
                        new_image_path = os.path.join(target_dir, new_filename)
                        shutil.move(image_path_wait, new_image_path)
                        logger.info(f"文件成功移动到: {new_image_path}")
                        # 将举报记录保存到 zc.json
                        zc_file = "zc.json"
                        try:
                            if os.path.exists(zc_file):
                                with open(zc_file, "r") as f:
                                    records = json.load(f)
                            else:
                                records = []
                            new_record = {
                                "username": player_value,  # 被举报人的用户名
                                "reporter": str(from_who),  # 举报人的 QQ，转换为字符串
                                "image_url": image_url  # 被举报图片的原始 URL
                            }
                            records.append(new_record)
                            with open(zc_file, "w") as f:
                                json.dump(records, f, indent=4)
                            logger.info(f"Added new record to zc.json: {new_record}")
                        except Exception as e:
                            logger.error(f"Failed to write to zc.json: {str(e)}")
                    return {"judgment": judgment_value, "player": player_value, "chat": chat_value, "all_player": all_player_value}
        except aiohttp.ClientError as e:
            if attempt == max_retries - 1:
                logger.error(f"AI 判断失败 (尝试 {max_retries} 次): {str(e)}")
                return f"判断失败: {str(e)}"
            logger.warning(f"AI请求错误: {str(e)}，尝试 {attempt + 1}/{max_retries}")
            await asyncio.sleep(backoff_factor ** attempt)
    return "判断失败: AI服务多次请求失败，请稍后再试"

# 获取 zc 记录的函数（内部使用）
async def get_zc_records():
    zc_file = "zc.json"
    if not os.path.exists(zc_file):
        return {"records": []}  # 如果文件不存在，返回空列表
    try:
        with open(zc_file, "r") as f:
            records = json.load(f)
        return {"records": records}
    except Exception as e:
        logger.error(f"Failed to read zc.json: {str(e)}")
        return {"error": f"Read failed: {str(e)}"}

# FastAPI 应用
app = FastAPI()

# 密码依赖函数
async def password_auth(password: str = Path(...)):
    correct_password = os.environ.get("ZC_API_PASSWORD")
    if not correct_password:
        raise HTTPException(status_code=500, detail="服务器配置错误: 密码未设置")
    if password != correct_password:
        raise HTTPException(status_code=401, detail="无效的密码")
    return True

# 修改后的 zc_records 接口端点：GET /{password}/zc_records
@app.get("/{password}/zc_records")
async def read_zc_records(password: str = Depends(password_auth)):
    records = await get_zc_records()
    if "error" in records:
        raise HTTPException(status_code=500, detail=records["error"])
    return JSONResponse(content=records)

# 添加 ai_judgment 接口端点：POST /{password}/ai_judgment
class AIJudgmentInput(BaseModel):
    image_url: str
    from_who: str  # 或 int，建议使用 str 以兼容 JSON
    player_list_str: Optional[str] = None  # 修改为 Optional[str] 以兼容 Python 3.9

class AIUniversalInput(BaseModel):
    image_url: str
    system_prompt: str
    user_prompt: str
    extract_rules: Optional[str] = None  # 可以是JSON字符串，将在函数中解析

@app.post("/{password}/ai_judgment")
async def ai_judgment_api(input_data: AIJudgmentInput, password: str = Depends(password_auth)):
    # 调用 ai_judgment 函数
    # 确保 from_who 参数的类型一致性
    from_who = input_data.from_who  # 保持字符串类型，ai_judgment 函数内部会处理

    result = await ai_judgment(input_data.image_url, from_who, input_data.player_list_str)
    if isinstance(result, dict):  # 如果是字典，直接返回
        return result
    else:  # 如果是字符串错误信息，返回 JSON 错误
        return JSONResponse(content={"error": result}, status_code=400)

@app.post("/{password}/ai")
async def ai_universal_general_api(input_data: AIUniversalInput, password: str = Depends(password_auth)):
    # 解析extract_rules（如果提供）
    extract_rules_dict = None
    if input_data.extract_rules:
        try:
            extract_rules_dict = json.loads(input_data.extract_rules)
        except json.JSONDecodeError as e:
            logger.error(f"解析extract_rules失败: {str(e)}")
            return JSONResponse(content={"error": f"extract_rules格式错误: {str(e)}"}, status_code=400)

    # 调用ai_universal_general函数
    result = await ai_universal_general(
        image_url=input_data.image_url,
        system_prompt=input_data.system_prompt,
        user_prompt=input_data.user_prompt,
        extract_rules=extract_rules_dict
    )

    if isinstance(result, dict):  # 如果是字典，直接返回
        return result
    else:  # 如果是字符串错误信息，返回JSON错误
        return JSONResponse(content={"error": result}, status_code=400)

# 主函数，启动前检查环境变量并运行 FastAPI
def main():
    try:
        # 从环境变量读取 AI 配置
        ai_base_url = os.environ.get("BASEURL")
        ai_api_key = os.environ.get("APIKEY")
        ai_model = os.environ.get("MODEL")
        # 增加日志输出环境变量
        logger.info(f"启动应用，环境变量: BASEURL={ai_base_url}, APIKEY={ai_api_key}, MODEL={ai_model}")
        check_env_variables()  # 启动前检查环境变量
        import uvicorn
        uvicorn.run(app, host="0.0.0.0", port=8000)
    except ValueError as e:
        logger.error(str(e))
        exit(1)

if __name__ == "__main__":
    main()