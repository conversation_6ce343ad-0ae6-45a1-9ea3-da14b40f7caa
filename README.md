# 战地五 AI 判断系统

一个基于 FastAPI 的战地五游戏聊天内容 AI 分析系统，用于自动检测游戏聊天中的语言攻击行为。

## 功能特性

- 🤖 **AI 智能分析**：使用大语言模型分析游戏截图中的聊天内容
- 🛡️ **语言攻击检测**：自动识别侮辱性词汇和不当言论
- 🖼️ **图片处理**：自动下载、压缩和处理游戏截图
- 🔒 **安全认证**：基于密码的 API 访问控制
- 🧹 **自动清理**：处理完成后自动删除临时图片文件
- 📊 **详细日志**：完整的操作日志记录

## 系统要求

- Python 3.9+
- 支持的操作系统：Windows、Linux、macOS
- 网络连接（用于下载图片和调用 AI API）

## 安装部署

### 方式一：直接运行

1. **克隆项目**
```bash
git clone <repository-url>
cd bfv_ai_judgment
```

2. **安装依赖**
```bash
pip install -r requirements.txt
```

3. **设置环境变量**
```bash
# Windows
set BASEURL=your_ai_api_base_url
set APIKEY=your_ai_api_key
set MODEL=your_ai_model_name
set ZC_API_PASSWORD=your_api_password

# Linux/macOS
export BASEURL=your_ai_api_base_url
export APIKEY=your_ai_api_key
export MODEL=your_ai_model_name
export ZC_API_PASSWORD=your_api_password
```

4. **启动服务**
```bash
python app.py
```

### 方式二：Docker 部署

1. **构建镜像**
```bash
docker build -t bfv-ai-judgment .
```

2. **运行容器**
```bash
docker run -d \
  -p 8000:8000 \
  -e BASEURL=your_ai_api_base_url \
  -e APIKEY=your_ai_api_key \
  -e MODEL=your_ai_model_name \
  -e ZC_API_PASSWORD=your_api_password \
  --name bfv-ai-judgment \
  bfv-ai-judgment
```

## API 接口

### 1. AI 判断接口

**端点**：`POST /{password}/ai_judgment`

**功能**：分析战地五游戏截图，检测聊天内容中的语言攻击行为

**请求参数**：
```json
{
  "image_url": "图片URL地址",
  "from_who": "举报人ID",
  "player_list_str": "当前服务器玩家名单（可选）"
}
```

**响应示例**：
```json
{
  "judgment": "1",
  "player": "违规玩家用户名",
  "chat": "提取的聊天内容",
  "all_player": "所有玩家用户名"
}
```

### 2. 通用 AI 接口

**端点**：`POST /{password}/ai`

**功能**：通用的 AI 图片分析接口，支持自定义提示词和提取规则

**请求参数**：
```json
{
  "image_url": "图片URL地址",
  "system_prompt": "系统提示词",
  "user_prompt": "用户提示词",
  "extract_rules": "提取规则（JSON字符串，可选）"
}
```

## 配置说明

### 环境变量

| 变量名 | 说明 | 必需 |
|--------|------|------|
| `BASEURL` | AI API 基础URL | ✅ |
| `APIKEY` | AI API 密钥 | ✅ |
| `MODEL` | AI 模型名称 | ✅ |
| `ZC_API_PASSWORD` | API 访问密码 | ✅ |

### 检测规则

系统会检测以下类型的语言攻击：
- 侮辱性词汇：`fw`、`ez`、`si ma`、`wo cao ni ma`、`cnm` 等
- 支持拼音和变形检测
- 可根据玩家名单验证用户身份

## 使用示例

### Python 示例

```python
import requests

# API 配置
password = "your_password"
url = f"http://localhost:8000/{password}/ai_judgment"

# 请求数据
data = {
    "image_url": "https://example.com/screenshot.png",
    "from_who": "12345",
    "player_list_str": "Player1,Player2,Player3"
}

# 发送请求
response = requests.post(url, json=data)
result = response.json()

print(f"判断结果: {result['judgment']}")
print(f"违规玩家: {result['player']}")
```

### cURL 示例

```bash
curl -X POST "http://localhost:8000/your_password/ai_judgment" \
  -H "Content-Type: application/json" \
  -d '{
    "image_url": "https://example.com/screenshot.png",
    "from_who": "12345",
    "player_list_str": "Player1,Player2,Player3"
  }'
```

## 工作流程

1. **接收请求**：API 接收包含图片URL的分析请求
2. **下载图片**：使用 curl 下载游戏截图到临时目录
3. **图片处理**：自动压缩图片至300KB以下以优化AI处理
4. **AI 分析**：调用大语言模型分析聊天内容
5. **结果提取**：使用正则表达式提取结构化结果
6. **自动清理**：删除临时图片文件
7. **返回结果**：返回分析结果给客户端

## 目录结构

```
bfv_ai_judgment/
├── app.py              # 主应用文件
├── requirements.txt    # Python 依赖
├── Dockerfile         # Docker 配置
├── test.py           # 测试脚本
└── README.md         # 项目说明
```

## 注意事项

- 🔒 请妥善保管 API 密钥和访问密码
- 📁 系统会自动清理临时文件，无需手动管理
- 🌐 确保网络连接稳定，以便下载图片和调用 AI API
- 📊 建议监控日志以了解系统运行状态
- ⚡ 图片大小会自动优化，但建议上传清晰的截图以获得更好的分析效果

## 故障排除

### 常见问题

1. **环境变量未设置**
   - 确保所有必需的环境变量都已正确设置

2. **图片下载失败**
   - 检查图片URL是否有效
   - 确认网络连接正常

3. **AI API 调用失败**
   - 验证 API 密钥和基础URL
   - 检查 AI 服务是否正常运行

4. **密码认证失败**
   - 确认 API 密码设置正确
   - 检查请求URL中的密码参数

## 许可证

本项目采用 MIT 许可证，详见 LICENSE 文件。

## 贡献

欢迎提交 Issue 和 Pull Request 来改进项目！
