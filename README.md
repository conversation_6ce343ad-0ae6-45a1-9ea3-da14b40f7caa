# 战地五 AI 判断系统

一个基于 FastAPI 的战地五游戏聊天内容 AI 分析系统，用于自动检测游戏聊天中的语言攻击行为。

## 功能特性

- 🤖 **AI 智能分析**：使用大语言模型分析游戏截图中的聊天内容
- 🛡️ **语言攻击检测**：自动识别侮辱性词汇和不当言论
- 🖼️ **图片处理**：自动下载、压缩和处理游戏截图
- 🔒 **安全认证**：基于密码的 API 访问控制
- 🧹 **自动清理**：处理完成后自动删除临时图片文件
- 📊 **详细日志**：完整的操作日志记录

## 系统要求

- Python 3.9+
- 支持的操作系统：Windows、Linux、macOS
- 网络连接（用于下载图片和调用 AI API）

## 安装部署

### 方式一：直接运行

1. **克隆项目**
```bash
git clone <repository-url>
cd bfv_ai_judgment
```

2. **安装依赖**
```bash
pip install -r requirements.txt
```

3. **设置环境变量**
```bash
# Windows
set BASEURL=your_ai_api_base_url
set APIKEY=your_ai_api_key
set MODEL=your_ai_model_name
set ZC_API_PASSWORD=your_api_password

# Linux/macOS
export BASEURL=your_ai_api_base_url
export APIKEY=your_ai_api_key
export MODEL=your_ai_model_name
export ZC_API_PASSWORD=your_api_password
```

4. **启动服务**
```bash
python app.py
```

### 方式二：Docker 部署

1. **构建镜像**
```bash
docker build -t bfv-ai-judgment .
```

2. **运行容器**
```bash
docker run -d \
  -p 8000:8000 \
  -e BASEURL=your_ai_api_base_url \
  -e APIKEY=your_ai_api_key \
  -e MODEL=your_ai_model_name \
  -e ZC_API_PASSWORD=your_api_password \
  --name bfv-ai-judgment \
  bfv-ai-judgment
```

## API 接口

### 1. AI 判断接口

**端点**：`POST /{password}/ai_judgment`

**功能**：分析战地五游戏截图，检测聊天内容中的语言攻击行为

**请求参数**：
```json
{
  "image_url": "图片URL地址",
  "from_who": "举报人ID",
  "player_list_str": "当前服务器玩家名单（可选）"
}
```

**响应示例**：
```json
{
  "judgment": "1",
  "player": "违规玩家用户名",
  "chat": "提取的聊天内容",
  "all_player": "所有玩家用户名"
}
```

### 2. 通用 AI 接口

**端点**：`POST /{password}/ai`

**功能**：通用的 AI 图片分析接口，支持自定义提示词和提取规则

**请求参数**：
```json
{
  "image_url": "图片URL地址",
  "system_prompt": "系统提示词",
  "user_prompt": "用户提示词",
  "extract_rules": "提取规则（JSON字符串，可选）"
}
```

**响应示例**：
- 如果提供了 `extract_rules`，返回提取的结构化数据
- 如果未提供 `extract_rules`，返回 AI 的原始文本响应

## 提取规则详解

### 什么是提取规则

提取规则（extract_rules）是一个 JSON 对象，用于从 AI 的文本响应中提取特定信息。它使用正则表达式来匹配和提取 AI 响应中的结构化数据。

### 提取规则格式

```json
{
  "字段名1": "正则表达式1",
  "字段名2": "正则表达式2",
  "字段名3": "正则表达式3"
}
```

### 工作原理

1. AI 根据你的提示词生成文本响应
2. 系统使用提取规则中的正则表达式匹配响应文本
3. 提取匹配到的内容并返回结构化的 JSON 数据

### 实际示例

#### 示例1：提取玩家用户名

**用户提示词**：
```
请提取出图片中的所有战地五玩家用户名。
提取结果请按以下格式输出：
<player>玩家1,玩家2,玩家3</player>
```

**提取规则**：
```json
{
  "player": "<player>(.*?)</player>"
}
```

**AI 响应**：
```
根据图片分析，我发现了以下玩家：
<player>SoldierBoy123,WarriorGirl456,BattleMaster789</player>
```

**最终返回**：
```json
{
  "player": "SoldierBoy123,WarriorGirl456,BattleMaster789"
}
```

#### 示例2：提取多种信息

**用户提示词**：
```
请分析图片中的战地五聊天内容，提取以下信息：
1. 所有玩家用户名
2. 聊天消息内容
3. 是否包含不当言论

请按以下格式输出：
<players>玩家1,玩家2</players>
<messages>消息1|消息2|消息3</messages>
<violation>是/否</violation>
<reason>违规原因</reason>
```

**提取规则**：
```json
{
  "players": "<players>(.*?)</players>",
  "messages": "<messages>(.*?)</messages>",
  "violation": "<violation>(.*?)</violation>",
  "reason": "<reason>(.*?)</reason>"
}
```

**AI 响应**：
```
分析完成，结果如下：
<players>Player1,Player2,Player3</players>
<messages>你好大家|这局打得不错|gg wp</messages>
<violation>否</violation>
<reason>未发现违规内容</reason>
```

**最终返回**：
```json
{
  "players": "Player1,Player2,Player3",
  "messages": "你好大家|这局打得不错|gg wp",
  "violation": "否",
  "reason": "未发现违规内容"
}
```

#### 示例3：复杂数据提取

**用户提示词**：
```
请分析游戏截图中的得分板信息：
<scoreboard>
队伍1得分: 500
队伍2得分: 450
MVP玩家: BestPlayer
击杀数: 25
死亡数: 8
</scoreboard>
```

**提取规则**：
```json
{
  "team1_score": "队伍1得分: (\\d+)",
  "team2_score": "队伍2得分: (\\d+)",
  "mvp_player": "MVP玩家: ([^\\n]+)",
  "kills": "击杀数: (\\d+)",
  "deaths": "死亡数: (\\d+)"
}
```

**最终返回**：
```json
{
  "team1_score": "500",
  "team2_score": "450",
  "mvp_player": "BestPlayer",
  "kills": "25",
  "deaths": "8"
}
```

### 正则表达式技巧

#### 常用模式

| 模式 | 说明 | 示例 |
|------|------|------|
| `(.*?)` | 非贪婪匹配任意字符 | `<tag>(.*?)</tag>` |
| `([^\\n]+)` | 匹配单行内容（不包含换行） | `玩家: ([^\\n]+)` |
| `(\\d+)` | 匹配数字 | `得分: (\\d+)` |
| `([A-Za-z0-9_]+)` | 匹配字母数字下划线 | `用户名: ([A-Za-z0-9_]+)` |
| `([^,]+)` | 匹配不包含逗号的内容 | 用于分隔列表项 |

#### 注意事项

1. **转义字符**：在 JSON 字符串中，反斜杠需要双重转义（`\\`）
2. **贪婪vs非贪婪**：使用 `(.*?)` 而不是 `(.*)` 避免过度匹配
3. **边界匹配**：使用明确的开始和结束标记
4. **测试正则**：建议先测试正则表达式的正确性

### 错误处理

如果正则表达式无法匹配到内容，对应字段的值将为 `"提取失败"`。

**示例**：
```json
{
  "existing_field": "成功提取的内容",
  "missing_field": "提取失败"
}
```

## 配置说明

### 环境变量

| 变量名 | 说明 | 必需 |
|--------|------|------|
| `BASEURL` | AI API 基础URL | ✅ |
| `APIKEY` | AI API 密钥 | ✅ |
| `MODEL` | AI 模型名称 | ✅ |
| `ZC_API_PASSWORD` | API 访问密码 | ✅ |

### 检测规则

系统会检测以下类型的语言攻击：
- 侮辱性词汇：`fw`、`ez`、`si ma`、`wo cao ni ma`、`cnm` 等
- 支持拼音和变形检测
- 可根据玩家名单验证用户身份

## 使用示例

### 示例1：AI 判断接口

```python
import requests

# API 配置
password = "your_password"
url = f"http://localhost:8000/{password}/ai_judgment"

# 请求数据
data = {
    "image_url": "https://example.com/screenshot.png",
    "from_who": "12345",
    "player_list_str": "Player1,Player2,Player3"
}

# 发送请求
response = requests.post(url, json=data)
result = response.json()

print(f"判断结果: {result['judgment']}")
print(f"违规玩家: {result['player']}")
print(f"聊天内容: {result['chat']}")
print(f"所有玩家: {result['all_player']}")
```

### 示例2：通用 AI 接口（无提取规则）

```python
import requests

password = "your_password"
url = f"http://localhost:8000/{password}/ai"

# 请求数据 - 不使用提取规则
data = {
    "image_url": "https://example.com/game_screenshot.png",
    "system_prompt": "你是一个游戏分析专家",
    "user_prompt": "请描述这张游戏截图中的内容，包括玩家、得分、地图等信息。"
}

response = requests.post(url, json=data)
result = response.json()

# 返回 AI 的原始文本响应
print("AI 分析结果:")
print(result)
```

### 示例3：通用 AI 接口（使用提取规则）

```python
import requests
import json

password = "your_password"
url = f"http://localhost:8000/{password}/ai"

# 定义提取规则
extract_rules = {
    "player_count": "玩家数量: (\\d+)",
    "map_name": "地图: ([^\\n]+)",
    "game_mode": "游戏模式: ([^\\n]+)",
    "team1_score": "队伍1得分: (\\d+)",
    "team2_score": "队伍2得分: (\\d+)"
}

# 请求数据 - 使用提取规则
data = {
    "image_url": "https://example.com/scoreboard.png",
    "system_prompt": "你是一个战地五游戏分析专家",
    "user_prompt": """
    请分析这张战地五得分板截图，提取以下信息：

    玩家数量: [数字]
    地图: [地图名称]
    游戏模式: [模式名称]
    队伍1得分: [数字]
    队伍2得分: [数字]

    请严格按照上述格式输出。
    """,
    "extract_rules": json.dumps(extract_rules)  # 转换为 JSON 字符串
}

response = requests.post(url, json=data)
result = response.json()

# 返回结构化数据
print("提取的游戏信息:")
print(f"玩家数量: {result.get('player_count', '未知')}")
print(f"地图名称: {result.get('map_name', '未知')}")
print(f"游戏模式: {result.get('game_mode', '未知')}")
print(f"队伍1得分: {result.get('team1_score', '未知')}")
print(f"队伍2得分: {result.get('team2_score', '未知')}")
```

### 示例4：复杂的聊天分析

```python
import requests
import json

password = "your_password"
url = f"http://localhost:8000/{password}/ai"

# 复杂的提取规则
extract_rules = {
    "total_messages": "消息总数: (\\d+)",
    "players": "参与聊天的玩家: ([^\\n]+)",
    "positive_messages": "正面消息: ([^\\n]*)",
    "negative_messages": "负面消息: ([^\\n]*)",
    "has_violation": "是否有违规: (是|否)",
    "violation_type": "违规类型: ([^\\n]*)",
    "recommendation": "处理建议: ([^\\n]+)"
}

data = {
    "image_url": "https://example.com/chat_screenshot.png",
    "system_prompt": "你是一个游戏社区管理员，专门分析游戏聊天内容",
    "user_prompt": """
    请详细分析这张聊天截图，提取以下信息：

    消息总数: [数字]
    参与聊天的玩家: [玩家1,玩家2,玩家3]
    正面消息: [消息内容，用|分隔]
    负面消息: [消息内容，用|分隔]
    是否有违规: [是/否]
    违规类型: [辱骂/垃圾信息/其他/无]
    处理建议: [建议内容]

    请仔细分析每条消息的情感倾向和内容。
    """,
    "extract_rules": json.dumps(extract_rules)
}

response = requests.post(url, json=data)
result = response.json()

print("聊天分析报告:")
print("=" * 50)
print(f"消息总数: {result.get('total_messages', '未知')}")
print(f"参与玩家: {result.get('players', '未知')}")
print(f"正面消息: {result.get('positive_messages', '无')}")
print(f"负面消息: {result.get('negative_messages', '无')}")
print(f"违规检测: {result.get('has_violation', '未知')}")
print(f"违规类型: {result.get('violation_type', '无')}")
print(f"处理建议: {result.get('recommendation', '无建议')}")
```

### cURL 示例

#### AI 判断接口

```bash
curl -X POST "http://localhost:8000/your_password/ai_judgment" \
  -H "Content-Type: application/json" \
  -d '{
    "image_url": "https://example.com/screenshot.png",
    "from_who": "12345",
    "player_list_str": "Player1,Player2,Player3"
  }'
```

#### 通用 AI 接口（无提取规则）

```bash
curl -X POST "http://localhost:8000/your_password/ai" \
  -H "Content-Type: application/json" \
  -d '{
    "image_url": "https://example.com/screenshot.png",
    "system_prompt": "你是一个游戏分析专家",
    "user_prompt": "请描述这张游戏截图的内容"
  }'
```

#### 通用 AI 接口（使用提取规则）

```bash
curl -X POST "http://localhost:8000/your_password/ai" \
  -H "Content-Type: application/json" \
  -d '{
    "image_url": "https://example.com/screenshot.png",
    "system_prompt": "你是一个战地五分析专家",
    "user_prompt": "请分析截图并按格式输出：\n玩家数量: [数字]\n地图名称: [名称]",
    "extract_rules": "{\"player_count\": \"玩家数量: (\\\\d+)\", \"map_name\": \"地图名称: ([^\\\\n]+)\"}"
  }'
```

## 工作流程

1. **接收请求**：API 接收包含图片URL的分析请求
2. **下载图片**：使用 curl 下载游戏截图到临时目录
3. **图片处理**：自动压缩图片至300KB以下以优化AI处理
4. **AI 分析**：调用大语言模型分析聊天内容
5. **结果提取**：使用正则表达式提取结构化结果
6. **自动清理**：删除临时图片文件
7. **返回结果**：返回分析结果给客户端

## 目录结构

```
bfv_ai_judgment/
├── app.py              # 主应用文件
├── requirements.txt    # Python 依赖
├── Dockerfile         # Docker 配置
├── test.py           # 测试脚本
└── README.md         # 项目说明
```

## 注意事项

- 🔒 请妥善保管 API 密钥和访问密码
- 📁 系统会自动清理临时文件，无需手动管理
- 🌐 确保网络连接稳定，以便下载图片和调用 AI API
- 📊 建议监控日志以了解系统运行状态
- ⚡ 图片大小会自动优化，但建议上传清晰的截图以获得更好的分析效果

## 故障排除

### 常见问题

1. **环境变量未设置**
   - 确保所有必需的环境变量都已正确设置

2. **图片下载失败**
   - 检查图片URL是否有效
   - 确认网络连接正常

3. **AI API 调用失败**
   - 验证 API 密钥和基础URL
   - 检查 AI 服务是否正常运行

4. **密码认证失败**
   - 确认 API 密码设置正确
   - 检查请求URL中的密码参数

## 许可证

本项目采用 MIT 许可证，详见 LICENSE 文件。

## 贡献

欢迎提交 Issue 和 Pull Request 来改进项目！
